2025-07-02 12:24:06,413 | INFO | patient_pipeline | Logging initialized. Writing to C:\Users\<USER>\Documents\CampaignKeywords\keywordsV1\pipeline.log
2025-07-02 12:24:06,413 | INFO | patient_pipeline | 🚀 Starting Appointment Keyword Pipeline
2025-07-02 12:24:06,415 | INFO | patient_pipeline | Loaded 160 unique campaign keywords.
2025-07-02 12:24:06,739 | INFO | patient_pipeline | Fetching appointment records: offset=0, batch=25
2025-07-02 12:24:06,751 | INFO | patient_pipeline | Fetched 25 appointment records.
2025-07-02 12:24:07,697 | INFO | patient_pipeline | Processing batch with 25 appointments using Gemini LLM
2025-07-02 12:24:07,697 | INFO | patient_pipeline | Building prompt for 25 patients and 160 campaign keywords
2025-07-02 12:24:07,701 | INFO | patient_pipeline | Reasoning required: True
2025-07-02 12:24:07,868 | INFO | patient_pipeline | Calling Gemini LLM...
2025-07-02 12:25:08,794 | INFO | patient_pipeline | Received signal 2. Requesting graceful shutdown...
2025-07-02 12:25:11,716 | INFO | patient_pipeline | LLM call complete. Parsing response...
2025-07-02 12:25:11,716 | WARNING | patient_pipeline | Appointment 24_17: expected 7 valid keywords after filtering, got 4
2025-07-02 12:25:11,717 | WARNING | patient_pipeline | Appointment 24_19: expected 7 valid keywords after filtering, got 4
2025-07-02 12:25:11,717 | WARNING | patient_pipeline | Appointment 24_128: expected 7 valid keywords after filtering, got 4
2025-07-02 12:25:11,717 | WARNING | patient_pipeline | Appointment 24_129: expected 7 valid keywords after filtering, got 4
2025-07-02 12:25:11,717 | WARNING | patient_pipeline | Appointment 24_147: expected 7 valid keywords after filtering, got 4
2025-07-02 12:25:11,717 | WARNING | patient_pipeline | Appointment 24_158: expected 7 valid keywords after filtering, got 4
2025-07-02 12:25:11,718 | WARNING | patient_pipeline | Appointment 24_165: expected 7 valid keywords after filtering, got 4
2025-07-02 12:25:11,718 | WARNING | patient_pipeline | Appointment 24_166: expected 7 valid keywords after filtering, got 4
2025-07-02 12:25:11,718 | WARNING | patient_pipeline | Appointment 24_173: expected 7 valid keywords after filtering, got 4
2025-07-02 12:25:11,718 | WARNING | patient_pipeline | Appointment 24_184: expected 7 valid keywords after filtering, got 4
2025-07-02 12:25:11,718 | WARNING | patient_pipeline | Appointment 24_185: expected 7 valid keywords after filtering, got 4
2025-07-02 12:25:11,719 | WARNING | patient_pipeline | Appointment 24_199: expected 7 valid keywords after filtering, got 4
2025-07-02 12:25:11,719 | WARNING | patient_pipeline | Appointment 24_200: expected 7 valid keywords after filtering, got 4
2025-07-02 12:25:11,719 | WARNING | patient_pipeline | Appointment 24_201: expected 7 valid keywords after filtering, got 4
2025-07-02 12:25:11,719 | WARNING | patient_pipeline | Appointment 24_282: expected 7 valid keywords after filtering, got 4
2025-07-02 12:25:11,719 | WARNING | patient_pipeline | Appointment 24_626: expected 7 valid keywords after filtering, got 4
2025-07-02 12:25:11,719 | WARNING | patient_pipeline | Appointment 24_883: expected 7 valid keywords after filtering, got 4
2025-07-02 12:25:11,720 | WARNING | patient_pipeline | Appointment 24_1328: expected 7 valid keywords after filtering, got 4
2025-07-02 12:25:11,720 | WARNING | patient_pipeline | Appointment 24_1503: expected 7 valid keywords after filtering, got 4
2025-07-02 12:25:11,720 | WARNING | patient_pipeline | Appointment 24_1660: expected 7 valid keywords after filtering, got 4
2025-07-02 12:25:11,720 | WARNING | patient_pipeline | Appointment 24_1706: expected 7 valid keywords after filtering, got 4
2025-07-02 12:25:11,720 | WARNING | patient_pipeline | Appointment 24_1922: expected 7 valid keywords after filtering, got 4
2025-07-02 12:25:11,720 | WARNING | patient_pipeline | Appointment 24_1949: expected 7 valid keywords after filtering, got 4
2025-07-02 12:25:11,721 | WARNING | patient_pipeline | Found 69 invalid keywords in batch: [('24_17', ['anemia', 'arthritis', 'congestive heart failure']), ('24_19', ['anemia', 'arthritis', 'congestive heart failure']), ('24_128', ['anemia', 'arthritis', 'congestive heart failure']), ('24_129', ['anemia', 'arthritis', 'congestive heart failure']), ('24_147', ['anemia', 'arthritis', 'congestive heart failure']), ('24_158', ['anemia', 'arthritis', 'congestive heart failure']), ('24_165', ['anemia', 'arthritis', 'congestive heart failure']), ('24_166', ['anemia', 'arthritis', 'congestive heart failure']), ('24_173', ['anemia', 'arthritis', 'congestive heart failure']), ('24_184', ['anemia', 'arthritis', 'congestive heart failure']), ('24_185', ['anemia', 'arthritis', 'congestive heart failure']), ('24_199', ['anemia', 'arthritis', 'congestive heart failure']), ('24_200', ['anemia', 'arthritis', 'congestive heart failure']), ('24_201', ['anemia', 'arthritis', 'congestive heart failure']), ('24_282', ['anemia', 'arthritis', 'congestive heart failure']), ('24_626', ['anemia', 'arthritis', 'congestive heart failure']), ('24_883', ['anemia', 'arthritis', 'congestive heart failure']), ('24_1328', ['anemia', 'arthritis', 'congestive heart failure']), ('24_1503', ['anemia', 'arthritis', 'congestive heart failure']), ('24_1660', ['anemia', 'arthritis', 'congestive heart failure']), ('24_1706', ['anemia', 'arthritis', 'congestive heart failure']), ('24_1922', ['anemia', 'arthritis', 'congestive heart failure']), ('24_1949', ['anemia', 'arthritis', 'congestive heart failure'])]
2025-07-02 12:25:11,721 | ERROR | patient_pipeline | Data validation error during LLM processing at offset 0: Too many invalid keywords in batch: 69 > 10. Skipping batch.
2025-07-02 12:25:11,721 | INFO | patient_pipeline | Pipeline stopped gracefully due to shutdown request. Processed 0 appointments total.

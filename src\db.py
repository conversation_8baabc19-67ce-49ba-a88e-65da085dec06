"""
Database operations module for appointment data and keyword storage.

This module handles all database interactions for the appointment keyword pipeline:
- Connection management with SQL Server using SQL authentication
- Appointment data retrieval from dbo.Appointment_EducationalContent_Keyword table
- Patient clinical data retrieval from clinical tables
- Storage of generated keywords using stored procedures

The module uses pyodbc for SQL Server connectivity and pandas for data manipulation.
All database operations are designed to be transaction-safe and efficient.
"""

import pyodbc
import pandas as pd
from contextlib import contextmanager
from typing import Any, Dict, List
import logging
import config
import time
import re

logger = logging.getLogger("patient_pipeline")

# SQL statements for stored procedure operations
# Note: Stored procedures are defined in sql/stored_procedures.sql
# This module calls the stored procedures rather than using direct SQL

def _require(var: str, name: str) -> str:
    """
    Validate that a required configuration variable is not empty.

    Args:
        var (str): The configuration variable value to check
        name (str): The name of the configuration variable for error messages

    Returns:
        str: The validated configuration variable value

    Raises:
        EnvironmentError: If the configuration variable is empty or None
    """
    if not var:
        raise EnvironmentError(f"Missing required config: {name}")
    return var

@contextmanager
def sql_connection():
    """
    Context manager for SQL Server database connections with retry logic.

    Creates a connection to the SQL Server database using configuration
    settings and ensures proper cleanup when the connection is no longer needed.
    Uses SQL Server authentication with username and password.

    Yields:
        pyodbc.Connection: Active database connection

    Raises:
        pyodbc.Error: If database connection fails after retries
        EnvironmentError: If required configuration is missing

    Example:
        >>> with sql_connection() as conn:
        ...     cursor = conn.cursor()
        ...     cursor.execute("EXEC sp_GetAppointmentBatch @Offset=0, @BatchSize=25")
    """
    # Validate server name to prevent injection
    server = _require(config.DB_SERVER, 'DB_SERVER')
    username = _require(config.DB_USERNAME, 'DB_USERNAME')
    password = _require(config.DB_PASSWORD, 'DB_PASSWORD')

    if not re.match(r'^[a-zA-Z0-9\\_.-]+$', server):
        raise ValueError(f"Invalid server name format: {server}")

    conn_str = (
        f"DRIVER={{{config.DB_DRIVER}}};"
        f"SERVER={server};"
        f"DATABASE={config.DB_NAME};"
        f"UID={username};"
        f"PWD={password};"
        f"Encrypt={config.DB_ENCRYPT};"
        f"TrustServerCertificate={config.DB_TRUST_CERT};"
    )

    # Retry connection with exponential backoff
    max_retries = 3
    for attempt in range(max_retries):
        try:
            conn = pyodbc.connect(conn_str, timeout=60)  # Increased timeout
            break
        except pyodbc.Error as e:
            if attempt == max_retries - 1:
                logger.error("Failed to connect to database after %d attempts: %s", max_retries, e)
                raise
            wait_time = 2 ** attempt  # Exponential backoff
            logger.warning("Database connection attempt %d failed, retrying in %d seconds: %s",
                         attempt + 1, wait_time, e)
            time.sleep(wait_time)

    try:
        yield conn
    finally:
        conn.close()

def ensure_tables(conn: pyodbc.Connection) -> None:
    """
    Verify that required database tables exist.

    Checks that the dbo.Appointment_EducationalContent_Keyword table exists.
    This is a production database, so we don't create or modify tables.

    Args:
        conn (pyodbc.Connection): Active database connection

    Raises:
        pyodbc.Error: If required tables don't exist
    """
    with conn.cursor() as cur:
        # Check if the required table exists
        cur.execute("""
            SELECT COUNT(*)
            FROM INFORMATION_SCHEMA.TABLES
            WHERE TABLE_SCHEMA = 'dbo'
            AND TABLE_NAME = 'Appointment_EducationalContent_Keyword'
        """)
        table_exists = cur.fetchone()[0]

        if not table_exists:
            raise RuntimeError("Required table dbo.Appointment_EducationalContent_Keyword does not exist")

def fetch_appointment_batch(conn: pyodbc.Connection, offset: int, batch: int) -> List[Dict[str, Any]]:
    """
    Fetch a batch of appointment records from the Appointment_EducationalContent_Keyword table.

    Retrieves appointment records with PatientID, ApptID, ApptNo, and ApptDate,
    optionally filtered by appointment date. Uses stored procedure for efficient pagination.

    Args:
        conn (pyodbc.Connection): Active database connection
        offset (int): Number of records to skip (for pagination)
        batch (int): Maximum number of appointment records to return

    Returns:
        List[Dict[str, Any]]: List of appointment records with keys:
            - PatientID: Patient identifier
            - ApptID: Appointment identifier
            - ApptNo: Appointment number
            - ApptDate: Appointment date

    Example:
        >>> with sql_connection() as conn:
        ...     appts = fetch_appointment_batch(conn, 0, 25)  # First 25 appointments
        ...     appts = fetch_appointment_batch(conn, 25, 25)  # Next 25 appointments
    """
    logger.info(f"Fetching appointment records: offset={offset}, batch={batch}")

    # Prepare parameters for stored procedure
    start_date = None
    if config.APPOINTMENT_START_DATE:
        # Validate date format to prevent SQL injection
        date_str = config.APPOINTMENT_START_DATE.strip()
        if not re.match(r'^\d{4}-\d{2}-\d{2}$', date_str):
            raise ValueError(f"Invalid date format in APPOINTMENT_START_DATE: {date_str}. Expected YYYY-MM-DD")
        start_date = date_str

    # Execute stored procedure
    with conn.cursor() as cur:
        cur.execute("EXEC sp_GetAppointmentBatch @Offset=?, @BatchSize=?, @StartDate=?",
                   (offset, batch, start_date))

        # Fetch results and convert to list of dictionaries
        columns = [column[0] for column in cur.description]
        appointments = []
        for row in cur.fetchall():
            appointment = dict(zip(columns, row))
            appointments.append(appointment)

    logger.info(f"Fetched {len(appointments)} appointment records.")
    return appointments

def fetch_patient_data(conn: pyodbc.Connection, patient_id: int) -> Dict[str, Any] | None:
    """
    Fetch comprehensive clinical data for a single patient using stored procedure.

    Retrieves and structures clinical data from multiple tables including:
    - Lab results and observations
    - Medical problems and diagnoses
    - Current medications
    - Known allergies

    The data is cleaned, deduplicated, and organized into a standardized format
    suitable for LLM processing.

    Args:
        conn (pyodbc.Connection): Active database connection
        patient_id (int): Unique patient identifier

    Returns:
        Dict[str, Any] | None: Structured patient data with keys:
            - Results: List of lab results and observations
            - Problems: List of medical problems/diagnoses
            - Medications: List of current medications
            - Allergies: List of known allergies
        Returns None if no clinical data found for the patient.

    Example:
        >>> with sql_connection() as conn:
        ...     data = fetch_patient_data(conn, 12345)
        ...     if data:
        ...         print(f"Patient has {len(data['Problems'])} problems")
    """
    # Execute stored procedure to get clinical data
    with conn.cursor() as cur:
        cur.execute("EXEC sp_GetPatientClinicalData @PatientID=?", (patient_id,))

        # Fetch results and convert to DataFrame for processing
        columns = [column[0] for column in cur.description]
        rows = cur.fetchall()

        if not rows:
            logger.warning(f"No clinical data found for patient_id={patient_id}. Skipping.")
            return None

        df = pd.DataFrame([dict(zip(columns, row)) for row in rows])

    # Map problem type codes to human-readable descriptions
    type_map = {
        "SocHx": "Social History",
        "PSH":   "Past Surgical History",
        "ROS":   "Review of Systems",
        "FamHx": "Family History",
        "PMH":   "Past Medical History",
    }

    # Process data by DataType from the new stored procedure format
    results = []
    problems = []
    medications = []
    allergies = []

    for _, row in df.iterrows():
        data_type = row.get('DataType', '')
        name = row.get('Name', '')
        value1 = row.get('Value1', '')
        value2 = row.get('Value2', '')
        value3 = row.get('Value3', '')

        if data_type == 'Result' and name:
            results.append({
                "ResultName": name,
                "ObservationValue": value1,
                "ObservationUnit": value2
            })
        elif data_type == 'Problem' and name:
            problem = {
                "ProblemName": name,
                "Type": type_map.get(value1, value1) if value1 else "Unknown"
            }
            if value2 and str(value2).strip():
                problem["DetailText"] = value2
            problems.append(problem)
        elif data_type == 'Medication' and name:
            medications.append({
                "MedicationName": name,
                "MedType": value1,
                "DoseQuantity": value2
            })
        elif data_type == 'Allergy' and name:
            allergies.append(name)

    # Return structured patient data
    return {
        "Results": results,
        "Problems": problems,
        "Medications": medications,
        "Allergies": allergies,
    }

def fetch_batch_appointment_data(conn: pyodbc.Connection, appointments: List[Dict[str, Any]],
                                skip_unchanged: bool = True) -> Dict[str, Any]:
    """
    Fetch clinical data for multiple appointments in a single batch operation.

    Takes a list of appointment records and fetches clinical data for each patient,
    handling errors gracefully by logging and skipping problematic patients.
    Only appointments with valid clinical data are included in the result.

    Optionally skips patients whose clinical data hasn't changed since last processing.

    Args:
        conn (pyodbc.Connection): Active database connection
        appointments (List[Dict[str, Any]]): List of appointment records with PatientID, ApptID, etc.
        skip_unchanged (bool): If True, skip patients whose clinical data hasn't changed

    Returns:
        Dict[str, Any]: Dictionary mapping appointment keys to their clinical data.
        Keys are formatted as "PatientID_ApptID" for uniqueness.

    Example:
        >>> with sql_connection() as conn:
        ...     appts = [{"PatientID": 12345, "ApptID": 67890, "ApptNo": "A001", "ApptDate": "2024-01-01"}]
        ...     batch_data = fetch_batch_appointment_data(conn, appts)
        ...     print(f"Successfully fetched data for {len(batch_data)} appointments")
    """
    batch: Dict[str, Any] = {}
    skipped_unchanged = 0

    for appt in appointments:
        try:
            patient_id = appt["PatientID"]
            appt_id = appt["ApptID"]
            appt_key = f"{patient_id}_{appt_id}"

            data = fetch_patient_data(conn, patient_id)
            if data:
                # Add appointment metadata to the clinical data
                # AppDate now available from dbo.Appointments join
                data["AppointmentInfo"] = {
                    "PatientID": patient_id,
                    "ApptID": appt_id,
                    "ApptNo": appt.get("ApptNo"),
                    "AppDate": appt.get("AppDate")  # From dbo.Appointments table join
                }

                # Check if patient data has changed (if skip_unchanged is enabled)
                if skip_unchanged and not has_patient_data_changed(conn, patient_id, data):
                    # Data hasn't changed, update with previous results instead of skipping
                    if update_appointment_with_previous_results(conn, patient_id, appt_id):
                        logger.info(f"Patient {patient_id} (Appt {appt_id}) updated with previous results (unchanged clinical data).")
                    else:
                        logger.info(f"Patient {patient_id} (Appt {appt_id}) clinical data unchanged. Skipping.")
                    skipped_unchanged += 1
                    continue

                batch[appt_key] = data
            else:
                logger.info(f"Patient {patient_id} (Appt {appt_id}) has no clinical data and will be skipped.")
        except Exception:
            logger.exception("Failed to fetch data for appointment PatientID=%s, ApptID=%s",
                           appt.get("PatientID"), appt.get("ApptID"))

    if skip_unchanged and skipped_unchanged > 0:
        logger.info(f"Processed {skipped_unchanged} appointments with unchanged clinical data using previous results.")

    return batch

def update_appointment_with_previous_results(conn: pyodbc.Connection, patient_id: int, appt_id: int) -> bool:
    """
    Update an appointment with previous processing results for the same patient.

    Args:
        conn (pyodbc.Connection): Active database connection
        patient_id (int): Patient identifier
        appt_id (int): Appointment identifier

    Returns:
        bool: True if update was successful, False otherwise
    """
    try:
        with conn.cursor() as cur:
            # Get the latest processing results for this patient
            cur.execute("""
                SELECT TOP 1 Keyword, Reasoning, Hashkey
                FROM dbo.Appointment_EducationalContent_Keyword
                WHERE PatientID = ? AND Keyword IS NOT NULL AND Hashkey IS NOT NULL
                ORDER BY KeywordLastUpdated DESC
            """, (patient_id,))

            result = cur.fetchone()
            if result:
                keyword, reasoning, hashkey = result

                # Update the current appointment with previous results
                cur.execute("""
                    EXEC sp_UpdateAppointmentKeywords
                    @PatientID=?, @ApptID=?, @Keywords=?, @Reasoning=?, @Hashkey=?, @Source=?, @KeywordSource=?, @ModifiedBy=?
                """, (patient_id, appt_id, keyword, reasoning, hashkey, "LLM", "Gemini", 1))

                # Get rows affected from the stored procedure result
                update_result = cur.fetchone()
                if update_result and update_result[0] > 0:
                    conn.commit()
                    return True
                else:
                    logger.warning(f"No rows updated for Patient {patient_id}, Appt {appt_id}")
                    return False
            else:
                logger.warning(f"No previous results found for Patient {patient_id}")
                return False

    except Exception as e:
        logger.error(f"Failed to update appointment with previous results for Patient {patient_id}, Appt {appt_id}: {e}")
        return False

def get_patient_latest_hash(conn: pyodbc.Connection, patient_id: int) -> str | None:
    """
    Get the most recent hash value for a patient's clinical data.

    Args:
        conn (pyodbc.Connection): Active database connection
        patient_id (int): Patient identifier

    Returns:
        str | None: Latest hash value for the patient, or None if no hash exists
    """
    try:
        with conn.cursor() as cur:
            cur.execute("EXEC sp_GetPatientLatestHash @PatientID=?", (patient_id,))
            result = cur.fetchone()
            if result and result[0]:  # Hashkey is the first column
                return result[0]
            return None
    except Exception as e:
        logger.warning(f"Failed to get latest hash for patient {patient_id}: {e}")
        return None

def has_patient_data_changed(conn: pyodbc.Connection, patient_id: int, current_data: Dict[str, Any]) -> bool:
    """
    Check if a patient's clinical data has changed since last processing.

    Args:
        conn (pyodbc.Connection): Active database connection
        patient_id (int): Patient identifier
        current_data (Dict[str, Any]): Current clinical data for the patient

    Returns:
        bool: True if data has changed or no previous hash exists, False if unchanged
    """
    # Get the latest hash for this patient
    latest_hash = get_patient_latest_hash(conn, patient_id)

    if latest_hash is None:
        # No previous hash exists, treat as changed
        return True

    # Calculate hash of current data
    from utils.hashing import canonicalize_patient_data, stable_hash
    clinical_data = {k: v for k, v in current_data.items() if k != "AppointmentInfo"}
    canonical_data = canonicalize_patient_data(clinical_data)
    current_hash = stable_hash(canonical_data)

    # Compare hashes
    return current_hash != latest_hash

def update_appointment_keywords(
    conn: pyodbc.Connection,
    results: Dict[str, Any],
    appointment_data: Dict[str, Any],
):
    """
    Update appointment keyword results in the database using stored procedure.

    Updates the Keyword column in dbo.Appointment_EducationalContent_Keyword table
    for each appointment. Maps LLM results back to appointment data using sequential order.

    Args:
        conn (pyodbc.Connection): Active database connection
        results (Dict[str, Any]): Dictionary mapping LLM response keys to
            keyword results (containing 'keywords' key)
        appointment_data (Dict[str, Any]): Dictionary mapping appointment keys to
            appointment data with AppointmentInfo

    Raises:
        pyodbc.Error: If database operation fails

    Example:
        >>> results = {
        ...     "1": {"keywords": ["diabetes", "hypertension"]},
        ...     "2": {"keywords": ["asthma", "allergies"]}
        ... }
        >>> appointment_data = {
        ...     "12345_67890": {"AppointmentInfo": {"PatientID": 12345, "ApptID": 67890}},
        ...     "12346_67891": {"AppointmentInfo": {"PatientID": 12346, "ApptID": 67891}}
        ... }
        >>> with sql_connection() as conn:
        ...     update_appointment_keywords(conn, results, appointment_data)
    """
    logger.info(f"Updating {len(results)} appointment keyword results in database.")

    # Map LLM results back to appointment data
    # The LLM returns results with numeric keys ("1", "2", etc.)
    # We need to match these to the original appointment data using order

    result_keys = sorted(results.keys(), key=lambda x: int(x) if x.isdigit() else 0)
    appointment_keys = sorted(appointment_data.keys())

    if len(result_keys) != len(appointment_keys):
        logger.error(f"Mismatch between LLM results ({len(result_keys)}) and appointments ({len(appointment_keys)})")
        return

    batch_updates = []

    for i, result_key in enumerate(result_keys):
        if i >= len(appointment_keys):
            logger.warning(f"No appointment data for result key {result_key}")
            continue

        appointment_key = appointment_keys[i]
        appt_info = appointment_data[appointment_key].get("AppointmentInfo", {})
        patient_id = appt_info.get("PatientID")
        appt_id = appt_info.get("ApptID")

        if not patient_id or not appt_id:
            logger.warning(f"Missing PatientID or ApptID for appointment {appointment_key}. Skipping.")
            continue

        # Get keywords and reasoning from LLM results
        llm_result = results[result_key]
        keywords = llm_result.get("keywords", [])
        reasoning_dict = llm_result.get("reasoning", {})

        # Serialize keywords as comma-separated string
        keywords_str = ", ".join(keywords) if keywords else ""

        # Serialize reasoning as JSON string (if reasoning exists)
        import json
        from datetime import datetime

        def datetime_serializer(obj):
            """JSON serializer for datetime objects"""
            if isinstance(obj, datetime):
                return obj.isoformat()
            raise TypeError(f"Object of type {type(obj)} is not JSON serializable")

        reasoning_str = json.dumps(reasoning_dict, default=datetime_serializer) if reasoning_dict else None

        # Get hash of clinical data for change detection
        clinical_data = {k: v for k, v in appointment_data[appointment_key].items()
                        if k != "AppointmentInfo"}
        from utils.hashing import canonicalize_patient_data, stable_hash
        canonical_data = canonicalize_patient_data(clinical_data)
        data_hash = stable_hash(canonical_data)

        batch_updates.append((patient_id, appt_id, keywords_str, reasoning_str, data_hash, "LLM", "Gemini", 1))
        logger.debug(f"Mapped result {result_key} to appointment {appointment_key}: {len(keywords)} keywords")

    if not batch_updates:
        logger.warning("No valid appointment updates to process.")
        return

    # Execute batch update using stored procedure
    try:
        with conn.cursor() as cur:
            rows_affected = 0
            for patient_id, appt_id, keywords_str, reasoning_str, data_hash, source, keyword_source, modified_by in batch_updates:
                cur.execute("""
                    EXEC sp_UpdateAppointmentKeywords
                    @PatientID=?, @ApptID=?, @Keywords=?, @Reasoning=?, @Hashkey=?, @Source=?, @KeywordSource=?, @ModifiedBy=?
                """, (patient_id, appt_id, keywords_str, reasoning_str, data_hash, source, keyword_source, modified_by))

                # Get rows affected from the stored procedure result
                result = cur.fetchone()
                if result:
                    rows_affected += result[0]

            conn.commit()
            logger.info(f"Update complete. {rows_affected} appointment records updated.")

    except Exception as e:
        logger.error("Error during appointment keyword update: %s", e)
        try:
            conn.rollback()
            logger.info("Transaction rolled back successfully.")
        except Exception as rollback_error:
            logger.error("Error during rollback: %s", rollback_error)
        raise